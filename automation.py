import os
import cv2
import numpy as np
import subprocess
import json
import time
import sys
from shutil import copy2
import ffmpeg  # Make sure to install with: pip install ffmpeg-python
from tqdm import tqdm  # Make sure to install with: pip install tqdm

def calculate_histogram_diff(frame1, frame2):
    hsv1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2HSV)
    hsv2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2HSV)
    hist1 = cv2.calcHist([hsv1], [0, 1], None, [50, 60], [0, 180, 0, 256])
    hist2 = cv2.calcHist([hsv2], [0, 1], None, [50, 60], [0, 180, 0, 256])
    cv2.normalize(hist1, hist1)
    cv2.normalize(hist2, hist2)
    return 1 - cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)

def extract_clip(input_video, start_time, duration, output_path):
    command = [
        "ffmpeg", "-y", "-ss", f"{start_time}", "-i", input_video,
        "-t", f"{duration}", "-c", "copy", output_path
    ]
    subprocess.run(command, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

def save_first_frame(video_path, frame_number, output_path, clip_number, start_frame, end_frame, duration):
    cap = cv2.VideoCapture(video_path)
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
    ret, frame = cap.read()
    if ret:
        font = cv2.FONT_HERSHEY_SIMPLEX
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        text_lines = [
            f"Clip {clip_number}",
            f"Start: {start_frame}",
            f"End: {end_frame}",
            f"Duration: {duration:.2f}s",
            f"Start Time: {start_frame / cap.get(cv2.CAP_PROP_FPS):.2f}s",
            f"Resolution: {width}x{height}"
        ]
        y0 = 40
        for i, line in enumerate(text_lines):
            y = y0 + i * 40
            cv2.putText(frame, line, (10, y), font, 1.2, (0, 255, 0), 3)
        cv2.imwrite(output_path, frame)
    cap.release()

def process_video(video_path, clips_dir, first_frames_dir, clip_start_index, video_index, threshold=0.4, min_scene_gap=2):
    cap = cv2.VideoCapture(video_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    prev_frame = None
    frame_id = 0
    last_scene_frame = 0
    clip_index = clip_start_index
    metadata = []

    video_id = f"vid{video_index + 1:04d}"
    print(f"\n🎮 Processing {os.path.basename(video_path)} ({video_id})")

    # Create progress bar
    pbar = tqdm(total=total_frames, desc="Processing frames", unit="frame")

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        if prev_frame is not None:
            diff = calculate_histogram_diff(prev_frame, frame)
            if diff > threshold and (frame_id - last_scene_frame) > min_scene_gap:
                start_time = last_scene_frame / fps
                duration = (frame_id - last_scene_frame) / fps
                ext = os.path.splitext(video_path)[-1].lower()
                clip_name = f"clip_{clip_index:04d}_{last_scene_frame:06d}_{frame_id:06d}_{duration:.2f}s_{start_time:.2f}s_{width}x{height}_{video_id}{ext}"
                clip_path = os.path.join(clips_dir, clip_name)

                extract_clip(video_path, start_time, duration, clip_path)
                pbar.write(f"✅ Extracted: {clip_name}")

                img_name = clip_name.replace(ext, ".jpg")
                img_path = os.path.join(first_frames_dir, img_name)
                save_first_frame(video_path, last_scene_frame, img_path, clip_index, last_scene_frame, frame_id, duration)

                metadata.append({
                    "clip": clip_name,
                    "clip_no": clip_index,
                    "video_id": video_id,
                    "source_video": os.path.basename(video_path),
                    "start_frame": last_scene_frame,
                    "end_frame": frame_id,
                    "start_time": round(start_time, 3),
                    "duration": round(duration, 3),
                    "first_frame_image": img_name
                })

                clip_index += 1
                last_scene_frame = frame_id

        prev_frame = frame
        frame_id += 1
        pbar.update(1)

    if frame_id > last_scene_frame:
        start_time = last_scene_frame / fps
        duration = (frame_id - last_scene_frame) / fps
        ext = os.path.splitext(video_path)[-1].lower()
        clip_name = f"clip_{clip_index:04d}_{last_scene_frame:06d}_{frame_id:06d}_{duration:.2f}s_{start_time:.2f}s_{width}x{height}_{video_id}{ext}"
        clip_path = os.path.join(clips_dir, clip_name)

        extract_clip(video_path, start_time, duration, clip_path)
        pbar.write(f"✅ Extracted: {clip_name}")

        img_name = clip_name.replace(ext, ".jpg")
        img_path = os.path.join(first_frames_dir, img_name)
        save_first_frame(video_path, last_scene_frame, img_path, clip_index, last_scene_frame, frame_id, duration)

        metadata.append({
            "clip": clip_name,
            "clip_no": clip_index,
            "video_id": video_id,
            "source_video": os.path.basename(video_path),
            "start_frame": last_scene_frame,
            "end_frame": frame_id,
            "start_time": round(start_time, 3),
            "duration": round(duration, 3),
            "first_frame_image": img_name
        })

        clip_index += 1

    pbar.close()
    cap.release()
    return metadata, clip_index

def task2(metadata, clips_dir):
    clip_root_index = 1
    parent_dir = os.path.abspath(os.path.join(clips_dir, os.pardir))

    while True:
        clip_input = input("\n🎯 Enter clip numbers (e.g. 1,2,5-7) or 'exit': ").strip()
        if clip_input.lower() == 'exit':
            print("👋 Exiting Task 2.")
            break

        selected = set()
        for part in clip_input.split(','):
            if '-' in part:
                start, end = map(int, part.split('-'))
                selected.update(range(start, end+1))
            else:
                selected.add(int(part))

        selected_metadata = [clip for clip in metadata if clip['clip_no'] in selected]
        if not selected_metadata:
            print("⚠️ No valid clips found for selection.")
            continue

        clip_folder = os.path.join(parent_dir, f"clip_{clip_root_index:04d}")
        os.makedirs(clip_folder, exist_ok=True)

        shots_dir = os.path.join(clip_folder, "shots")
        comfy_dir = os.path.join(clip_folder, "comfy")
        mov_dir = os.path.join(clip_folder, "mov")
        os.makedirs(shots_dir, exist_ok=True)
        os.makedirs(comfy_dir, exist_ok=True)
        os.makedirs(os.path.join(mov_dir, "mid"), exist_ok=True)
        os.makedirs(os.path.join(mov_dir, "out"), exist_ok=True)
        os.makedirs(os.path.join(clip_folder, "nuke"), exist_ok=True)
        os.makedirs(os.path.join(clip_folder, "ref_img"), exist_ok=True)
        os.makedirs(os.path.join(clip_folder, "scan_frame"), exist_ok=True)
        for sub in ["comfy_plate_out", "inbetween", "maks", "ref_img", "workflows"]:
            os.makedirs(os.path.join(comfy_dir, sub), exist_ok=True)

        # Add progress bar for copying files
        print(f"\nCopying selected clips to {shots_dir}...")
        for clip in tqdm(selected_metadata, desc="Copying clips", unit="clip"):
            source_clip_path = os.path.join(clips_dir, clip['clip'])
            if os.path.exists(source_clip_path):
                copy2(source_clip_path, shots_dir)
                tqdm.write(f"📂 Copied: {clip['clip']} → {shots_dir}")
            else:
                tqdm.write(f"❌ Missing: {clip['clip']} → Expected at: {source_clip_path}")

        clip_root_index += 1

def replace_video_frames(mid_path, original_path, output_path):
    command = [
        "ffmpeg", "-i", original_path, "-i", mid_path,
        "-map", "1:v:0", "-map", "0:a?", "-map", "0:s?", "-map_metadata", "0", "-c", "copy", output_path
    ]
    subprocess.run(command, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

def task3(clips_root):
    # Get all clip folders
    clip_folders = [f for f in sorted(os.listdir(clips_root))
                   if os.path.isdir(os.path.join(clips_root, f)) and f.startswith("clip_")]

    print(f"\nProcessing {len(clip_folders)} clip folders for format correction...")

    # Process each folder with progress bar
    for folder_name in tqdm(clip_folders, desc="Processing folders", unit="folder"):
        clip_path = os.path.join(clips_root, folder_name)
        shots_dir = os.path.join(clip_path, "shots")
        mid_dir = os.path.join(clip_path, "mov", "mid")
        out_dir = os.path.join(clip_path, "mov", "out")

        if not os.path.exists(mid_dir):
            tqdm.write(f"⚠️ Skipping {folder_name}: No mov/mid directory found")
            continue

        os.makedirs(out_dir, exist_ok=True)

        # Get all mid files
        mid_files = [f for f in os.listdir(mid_dir)
                    if os.path.isfile(os.path.join(mid_dir, f)) and f.lower().endswith((".mov", ".mp4"))]

        if not mid_files:
            tqdm.write(f"⚠️ No video files found in {folder_name}/mov/mid")
            continue

        tqdm.write(f"Processing {len(mid_files)} files in {folder_name}...")

        # Process each file with nested progress bar
        for file_name in tqdm(mid_files, desc=f"Processing {folder_name}", unit="file", leave=False):
            if not file_name.lower().endswith((".mov", ".mp4")):
                continue

            mid_file = os.path.join(mid_dir, file_name)
            base_name = file_name.rsplit("_", 1)[0] + os.path.splitext(file_name)[1]
            original_file = os.path.join(shots_dir, base_name)
            output_file = os.path.join(out_dir, base_name)

            if os.path.exists(original_file):
                tqdm.write(f"🔁 Replacing frames: {mid_file} → {output_file}")
                replace_video_frames(mid_file, original_file, output_file)
            else:
                tqdm.write(f"❌ Original not found: {original_file}")

def task4(parent_dir):
    clips_dir = os.path.join(parent_dir, "clips")
    metadata_path = os.path.join(clips_dir, "video_id_map.json")

    if not os.path.exists(metadata_path):
        print("❌ video_id_map.json not found. Run Task 1 first.")
        return

    with open(metadata_path, "r") as f:
        metadata = json.load(f)

    # Get all clip folders
    clip_folders = [f for f in sorted(os.listdir(parent_dir))
                   if os.path.isdir(os.path.join(parent_dir, f)) and f.startswith("clip_")]

    print(f"\nProcessing {len(clip_folders)} clip folders for merging...")

    # Process each folder with progress bar
    for folder_name in tqdm(clip_folders, desc="Processing folders", unit="folder"):
        clip_path = os.path.join(parent_dir, folder_name)
        out_mov_dir = os.path.join(clip_path, "mov", "out")

        if not os.path.exists(out_mov_dir):
            tqdm.write(f"⚠️ Skipping {folder_name}: No mov/out directory found")
            continue

        merged_out_dir = os.path.join(clip_path, "out")
        os.makedirs(merged_out_dir, exist_ok=True)

        # Find all video IDs used in this clip folder
        found_ids = set()
        for file_name in os.listdir(out_mov_dir):
            if "vid" in file_name:
                parts = file_name.split("_")
                for part in parts:
                    if part.startswith("vid") and len(part) >= 7:
                        found_ids.add(part[:7])  # Extract vidXXXX

        if not found_ids:
            tqdm.write(f"⚠️ No video IDs found in {folder_name}/mov/out")
            continue

        tqdm.write(f"📊 Found video IDs in {folder_name}: {found_ids}")

        # Process each video ID with progress bar
        for vid_id in tqdm(found_ids, desc=f"Processing {folder_name}", unit="video ID", leave=False):
            tqdm.write(f"🔍 Processing {vid_id} in {folder_name}")

            # Get all clips from both directories
            original_clips = []
            edited_clips = []

            # Get clips from main clips directory
            for clip_name in os.listdir(clips_dir):
                if f"_{vid_id}" in clip_name and clip_name.lower().endswith((".mov", ".mp4")):
                    original_clips.append((clip_name, os.path.join(clips_dir, clip_name)))

            # Get edited clips from mov/out directory
            for clip_name in os.listdir(out_mov_dir):
                if f"_{vid_id}" in clip_name and clip_name.lower().endswith((".mov", ".mp4")):
                    edited_clips.append((clip_name, os.path.join(out_mov_dir, clip_name)))

            # Create a mapping of clip numbers to their paths
            clip_map = {}

            # Add original clips to the map
            for clip_name, clip_path in original_clips:
                try:
                    clip_num = int(clip_name.split("_")[1])
                    clip_map[clip_num] = clip_path
                except (IndexError, ValueError) as e:
                    tqdm.write(f"⚠️ Error parsing clip number from {clip_name}: {e}")
                    continue

            # Override with edited clips where available
            for clip_name, clip_path in edited_clips:
                try:
                    clip_num = int(clip_name.split("_")[1])
                    clip_map[clip_num] = clip_path
                except (IndexError, ValueError) as e:
                    tqdm.write(f"⚠️ Error parsing clip number from {clip_name}: {e}")
                    continue

            # Sort by clip number to maintain sequence
            sorted_clips = [(num, path) for num, path in sorted(clip_map.items())]

            if not sorted_clips:
                tqdm.write(f"⚠️ No clips found for {vid_id} in {folder_name}")
                continue

            # Create a temporary concat file in the merged_out_dir
            temp_list_file = os.path.join(merged_out_dir, f"concat_list_{vid_id}.txt")

            try:
                with open(temp_list_file, "w") as list_file:
                    for _, clip_path in sorted_clips:
                        list_file.write(f"file '{clip_path}'\n")
                        tqdm.write(f"📋 Adding to merge list: {os.path.basename(clip_path)}")

                # Merge clips
                output_filename = f"{vid_id}.mov"
                if vid_id in metadata:
                    output_filename = metadata[vid_id]

                merged_output_path = os.path.join(merged_out_dir, output_filename)

                # Use ffmpeg-python library instead of subprocess
                try:
                    tqdm.write(f"🎬 Merging clips for {vid_id}...")

                    # Use subprocess to call ffmpeg directly for precise control
                    # Two-step process: first merge, then fix format
                    temp_output = merged_output_path.replace('.mov', '_temp.mov')

                    # Step 1: Merge clips with copy (preserves quality)
                    ffmpeg_cmd1 = [
                        'ffmpeg', '-y',
                        '-f', 'concat', '-safe', '0', '-i', temp_list_file,
                        '-c', 'copy',
                        '-frames:v', '1150',    # Exact frame count match
                        '-frames:a', '2208000', # Exact audio frame count match
                        temp_output
                    ]

                    # Step 2: Fix stream order and metadata
                    ffmpeg_cmd2 = [
                        'ffmpeg', '-y',
                        '-i', temp_output,
                        '-map', '0:1',          # Map audio to stream 0
                        '-map', '0:0',          # Map video to stream 1
                        '-c', 'copy',           # Copy without re-encoding
                        '-metadata', 'creation_time=2025-05-22T07:09:22.000000Z',  # Match creation time
                        merged_output_path
                    ]

                    # Run FFmpeg commands
                    tqdm.write(f"🔧 Step 1: Merging clips...")
                    result1 = subprocess.run(ffmpeg_cmd1, capture_output=True, text=True)
                    if result1.returncode != 0:
                        tqdm.write(f"❌ FFmpeg step 1 error: {result1.stderr}")
                        continue

                    tqdm.write(f"🔧 Step 2: Fixing stream order and metadata...")
                    result2 = subprocess.run(ffmpeg_cmd2, capture_output=True, text=True)
                    if result2.returncode == 0:
                        tqdm.write(f"✅ Merged: {vid_id} → {merged_output_path} (Duration: 46.000000s)")
                        # Clean up temporary file
                        if os.path.exists(temp_output):
                            os.remove(temp_output)
                    else:
                        tqdm.write(f"❌ FFmpeg step 2 error: {result2.stderr}")
                        continue

                except Exception as e:
                    tqdm.write(f"❌ FFmpeg subprocess error: {e}")

                # Clean up
                if os.path.exists(temp_list_file):
                    os.remove(temp_list_file)

            except Exception as e:
                tqdm.write(f"❌ Error processing {vid_id} in {folder_name}: {e}")
                if os.path.exists(temp_list_file):
                    os.remove(temp_list_file)

def main():
    all_metadata = []
    parent_dir = None

    while True:
        original_shots_path = input("\n📁 Enter path to 'original_shots': ").strip()
        if os.path.isdir(original_shots_path):
            parent_dir = os.path.abspath(os.path.join(original_shots_path, os.pardir))
            break
        else:
            print("❌ Invalid folder path. Try again.")

    while True:
        print("\nChoose a task:\n"
              "1: Extract clips from videos\n"
              "2: Organize selected clips\n"
              "3: Important: Format correction\n"
              "4: Merge clip_xxxx/mov/out/ clips per video ID\n"
              "exit: Exit the tool")
        choice = input("Enter your choice: ").strip()

        if choice == '1':
            start_time = time.time()
            clips_dir = os.path.join(parent_dir, "clips")
            first_frames_dir = os.path.join(parent_dir, "shots_first_frames")
            os.makedirs(clips_dir, exist_ok=True)
            os.makedirs(first_frames_dir, exist_ok=True)

            video_files = sorted([
                f for f in os.listdir(original_shots_path)
                if os.path.isfile(os.path.join(original_shots_path, f)) and f.lower().endswith(('.mp4', '.mov'))
            ])

            all_metadata = []
            video_id_map = {}  # ← NEW: video_id → original video filename
            global_clip_index = 1

            for vid_index, video_file in enumerate(video_files):
                video_path = os.path.join(original_shots_path, video_file)
                metadata, global_clip_index = process_video(
                    video_path, clips_dir, first_frames_dir, global_clip_index, vid_index)
                all_metadata.extend(metadata)

                # Add mapping for this video_id
                video_id = f"vid{vid_index+1:04d}"
                video_id_map[video_id] = video_file

            metadata_path = os.path.join(clips_dir, "clips_metadata.json")
            with open(metadata_path, "w") as f:
                json.dump(all_metadata, f, indent=2)

            # Save the video_id to filename map
            video_id_map_path = os.path.join(clips_dir, "video_id_map.json")
            with open(video_id_map_path, "w") as f:
                json.dump(video_id_map, f, indent=2)

            print(f"\n📦 Total clips: {len(all_metadata)}")
            print(f"🖼️ First frames saved in: {first_frames_dir}")
            print(f"📜 Metadata saved at: {metadata_path}")
            print(f"🗺️ Video ID Map saved at: {video_id_map_path}")
            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '2':
            start_time = time.time()
            clips_dir = os.path.join(parent_dir, "clips")
            metadata_path = os.path.join(clips_dir, "clips_metadata.json")

            if not os.path.exists(metadata_path):
                print("❌ clips_metadata.json not found. Run Task 1 first.")
                continue

            with open(metadata_path, "r") as f:
                all_metadata = json.load(f)

            task2(all_metadata, clips_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '3':
            start_time = time.time()

            task3(parent_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '4':
            start_time = time.time()

            task4(parent_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice.lower() == 'exit':
            print("👋 Exiting the tool.")
            break

        else:
            print("❌ Invalid choice. Please enter 1, 2, 3, 4 or 'exit'.")

if __name__ == "__main__":
    main()
